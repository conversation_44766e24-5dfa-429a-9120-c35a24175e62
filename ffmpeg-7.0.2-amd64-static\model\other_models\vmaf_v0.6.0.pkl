(dp0
S'param_dict'
p1
(dp2
S'norm_type'
p3
S'clip_0to1'
p4
sS'score_clip'
p5
(lp6
F0.0
aF100.0
asS'C'
p7
F4.0
sS'nu'
p8
F0.9
sS'gamma'
p9
F0.05
ssS'model_dict'
p10
(dp11
S'model'
p12
Nsg3
S'linear_rescale'
p13
sg5
g6
sS'feature_names'
p14
(lp15
S'VMAF_feature_adm2_score'
p16
aS'VMAF_feature_motion2_score'
p17
aS'VMAF_feature_vif_scale0_score'
p18
aS'VMAF_feature_vif_scale1_score'
p19
aS'VMAF_feature_vif_scale2_score'
p20
aS'VMAF_feature_vif_scale3_score'
p21
asS'intercepts'
p22
(lp23
F-0.2545454545454545
aF-1.445823604286296
aF-0.0021166363925847658
aF-0.08203930409252254
aF-0.28580569810409906
aF-0.4801667335681057
aF-0.7764750530230472
asS'model_type'
p24
S'LIBSVMNUSVR'
p25
sS'slopes'
p26
(lp27
F0.011818181818181818
aF2.445823604286296
aF0.05347617487103272
aF1.0820392911303012
aF1.2858060650284484
aF1.480167484761588
aF1.7764758964900356
asS'feature_dict'
p28
(dp29
S'VMAF_feature'
p30
(lp31
S'vif_scale0'
p32
aS'vif_scale1'
p33
aS'vif_scale2'
p34
aS'vif_scale3'
p35
aS'adm2'
p36
aS'motion2'
p37
asss.