# FunASR WebSocket 服务器 Docker Compose 配置
# 使用方法: docker-compose up -d

version: '3.8'

services:
  funasr-server:
    # 镜像名称
    image: asr-cpu:latest
    
    # 容器名称
    container_name: funasr-server
    
    # 端口映射 - 主机端口:容器端口
    ports:
      - "10098:10098"  # FunASR WebSocket 主服务端口
      - "10095:10095"  # 额外服务端口
    
    # 目录映射 - 主机路径:容器路径
    volumes:
      # 模型文件目录 (必需)
      - "./models:/workspace/models"
      # 训练模型目录 (必需)
      - "./save_model:/workspace/save_model"
      # 可选: 自定义热词文件
      # - "./custom_hotwords.txt:/workspace/hotwords.txt"
    
    # 环境变量配置 (可选，使用默认值)
    environment:
      # SSL证书文件 (0=不使用SSL, 1=使用SSL)
      - CERTFILE=0

      # WebSocket服务器监听端口
      - PORT=10098

      # VAD (语音活动检测) 模型路径
      - VAD_DIR=/workspace/models/damo/speech_fsmn_vad_zh-cn-16k-common-onnx

      # 离线ASR模型路径
      - MODEL_DIR=/workspace/save_model

      # 在线ASR模型路径
      - ONLINE_MODEL_DIR=/workspace/save_model

      # 标点符号模型路径
      - PUNC_DIR=/workspace/models/damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx

      # 语言模型路径
      - LM_DIR=/workspace/models/lm/resource

      # 逆文本标准化模型路径
      - ITN_DIR=/workspace/models/thuduj12/fst_itn_zh

      # 热词文件路径
      - HOTWORD_FILE=/workspace/hotwords.txt
    
    # 重启策略
    restart: unless-stopped
    
    # 健康检查 (可选)
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10098/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 资源限制 (可选)
    deploy:
      resources:
        limits:
          # 内存限制
          memory: 4G
          # CPU限制
          cpus: '2.0'
        reservations:
          # 内存预留
          memory: 2G
          # CPU预留
          cpus: '1.0'

# 网络配置 (可选)
networks:
  default:
    name: funasr-network
    driver: bridge

# 数据卷配置 (可选)
volumes:
  # 如果需要持久化日志
  funasr-logs:
    driver: local
