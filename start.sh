#!/bin/bash
set -e

echo "=== FunASR Server Startup ==="
echo "CERTFILE: ${CERTFILE}"
echo "PORT: ${PORT}"
echo "VAD_DIR: ${VAD_DIR}"
echo "MODEL_DIR: ${MODEL_DIR}"
echo "ONLINE_MODEL_DIR: ${ONLINE_MODEL_DIR}"
echo "PUNC_DIR: ${PUNC_DIR}"
echo "LM_DIR: ${LM_DIR}"
echo "ITN_DIR: ${ITN_DIR}"
echo "HOTWORD_FILE: ${HOTWORD_FILE}"
echo "=========================="

# Create necessary directories and copy models if they exist in /workspace/models
echo "Setting up model directories..."

# Create save_model directory if it doesn't exist
mkdir -p /workspace/save_model

# Check if models exist in /workspace/models and copy them to the expected locations
if [ -d "/workspace/models/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-onnx" ]; then
    echo "Copying offline ASR model to ${MODEL_DIR}..."
    cp -r /workspace/models/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-onnx/* /workspace/save_model/
fi

if [ -d "/workspace/models/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-online-onnx" ]; then
    echo "Copying online ASR model to ${ONLINE_MODEL_DIR}..."
    # Both models use the same directory according to your ENV config
    cp -r /workspace/models/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-online-onnx/* /workspace/save_model/
    echo "Online model copied to /workspace/save_model/"
fi

# Create LM resource directory and copy LM model if it exists
if [ -d "/workspace/models/damo/speech_ngram_lm_zh-cn-ai-wesp-fst" ]; then
    echo "Setting up LM directory..."
    mkdir -p /workspace/FunASR/runtime/tools/lm/resource
    cp -r /workspace/models/damo/speech_ngram_lm_zh-cn-ai-wesp-fst/* /workspace/FunASR/runtime/tools/lm/resource/
fi

cd /workspace/FunASR/runtime

echo "Current directory: $(pwd)"
echo "Starting server with custom parameters..."
echo "Command: bash run_server_2pass.sh --certfile '${CERTFILE}' --port '${PORT}' --vad_dir '${VAD_DIR}' --model_dir '${MODEL_DIR}' --online_model_dir '${ONLINE_MODEL_DIR}' --punc_dir '${PUNC_DIR}' --lm_dir '${LM_DIR}' --itn_dir '${ITN_DIR}' --hotword '${HOTWORD_FILE}'"

# Start the server with all custom parameters
echo "About to execute run_server_2pass.sh..."
exec bash run_server_2pass.sh \
    --certfile  "${CERTFILE}" \
    --port      "${PORT}" \
    --vad_dir   "${VAD_DIR}" \
    --model_dir "${MODEL_DIR}" \
    --online_model_dir "${ONLINE_MODEL_DIR}" \
    --punc_dir  "${PUNC_DIR}" \
    --lm_dir    "${LM_DIR}" \
    --itn_dir   "${ITN_DIR}" \
    --hotword   "${HOTWORD_FILE}"
