#!/bin/bash
set -e

echo "正在启动 FunASR WebSocket 服务器..."
echo "监听端口: 10098"

# 切换到 FunASR 运行目录
cd /workspace/FunASR/runtime

# 检查关键路径，只打印错误信息
if [ ! -d "${VAD_DIR}" ]; then
    echo "错误: VAD_DIR 不存在 - ${VAD_DIR}"
    exit 1
fi

if [ ! -d "${MODEL_DIR}" ]; then
    echo "错误: MODEL_DIR 不存在 - ${MODEL_DIR}"
    exit 1
fi

if [ ! -d "${ONLINE_MODEL_DIR}" ]; then
    echo "错误: ONLINE_MODEL_DIR 不存在 - ${ONLINE_MODEL_DIR}"
    exit 1
fi

if [ ! -d "${PUNC_DIR}" ]; then
    echo "错误: PUNC_DIR 不存在 - ${PUNC_DIR}"
    exit 1
fi

if [ ! -d "${LM_DIR}" ]; then
    echo "错误: LM_DIR 不存在 - ${LM_DIR}"
    exit 1
fi

if [ ! -d "${ITN_DIR}" ]; then
    echo "错误: ITN_DIR 不存在 - ${ITN_DIR}"
    exit 1
fi

if [ ! -f "${HOTWORD_FILE}" ]; then
    echo "警告: 热词文件不存在，创建空文件 - ${HOTWORD_FILE}"
    touch "${HOTWORD_FILE}"
fi

echo "启动服务器..."

# 启动 FunASR WebSocket 服务器
bash run_server_2pass.sh \
    --certfile  "${CERTFILE}" \
    --port      10098 \
    --vad-dir   "${VAD_DIR}" \
    --model-dir "${MODEL_DIR}" \
    --online-model-dir "${ONLINE_MODEL_DIR}" \
    --punc-dir  "${PUNC_DIR}" \
    --lm-dir    "${LM_DIR}" \
    --itn-dir   "${ITN_DIR}" \
    --hotword   "${HOTWORD_FILE}" 2>&1

# 保持容器运行
tail -f /dev/null