#!/bin/bash
set -e
set -x


cd /workspace/FunASR/runtime

echo "=== Checking all paths before execution ==="

echo "Checking VAD_DIR: ${VAD_DIR}"
if [ -d "${VAD_DIR}" ]; then
    echo "✓ VAD_DIR exists"
    file_count=$(ls -1 "${VAD_DIR}" 2>/dev/null | wc -l)
    echo "  Files in VAD_DIR: $file_count"
    if [ $file_count -eq 0 ]; then
        echo "  ⚠️  VAD_DIR is EMPTY!"
    else
        echo "  📁 VAD_DIR contents: $(ls -1 "${VAD_DIR}" | head -3 | tr '\n' ' ')..."
    fi
else
    echo "✗ VAD_DIR does NOT exist!"
fi

echo "Checking MODEL_DIR: ${MODEL_DIR}"
if [ -d "${MODEL_DIR}" ]; then
    echo "✓ MODEL_DIR exists"
    file_count=$(ls -1 "${MODEL_DIR}" 2>/dev/null | wc -l)
    echo "  Files in MODEL_DIR: $file_count"
    if [ $file_count -eq 0 ]; then
        echo "  ⚠️  MODEL_DIR is EMPTY!"
    else
        echo "  📁 MODEL_DIR contents: $(ls -1 "${MODEL_DIR}" | head -3 | tr '\n' ' ')..."
    fi
else
    echo "✗ MODEL_DIR does NOT exist!"
fi

echo "Checking ONLINE_MODEL_DIR: ${ONLINE_MODEL_DIR}"
if [ -d "${ONLINE_MODEL_DIR}" ]; then
    echo "✓ ONLINE_MODEL_DIR exists"
    file_count=$(ls -1 "${ONLINE_MODEL_DIR}" 2>/dev/null | wc -l)
    echo "  Files in ONLINE_MODEL_DIR: $file_count"
    if [ $file_count -eq 0 ]; then
        echo "  ⚠️  ONLINE_MODEL_DIR is EMPTY!"
    else
        echo "  📁 ONLINE_MODEL_DIR contents: $(ls -1 "${ONLINE_MODEL_DIR}" | head -3 | tr '\n' ' ')..."
    fi
else
    echo "✗ ONLINE_MODEL_DIR does NOT exist!"
fi

echo "Checking PUNC_DIR: ${PUNC_DIR}"
if [ -d "${PUNC_DIR}" ]; then
    echo "✓ PUNC_DIR exists"
    file_count=$(ls -1 "${PUNC_DIR}" 2>/dev/null | wc -l)
    echo "  Files in PUNC_DIR: $file_count"
    if [ $file_count -eq 0 ]; then
        echo "  ⚠️  PUNC_DIR is EMPTY!"
    else
        echo "  📁 PUNC_DIR contents: $(ls -1 "${PUNC_DIR}" | head -3 | tr '\n' ' ')..."
    fi
else
    echo "✗ PUNC_DIR does NOT exist!"
fi

echo "Checking LM_DIR: ${LM_DIR}"
if [ -d "${LM_DIR}" ]; then
    echo "✓ LM_DIR exists"
    file_count=$(ls -1 "${LM_DIR}" 2>/dev/null | wc -l)
    echo "  Files in LM_DIR: $file_count"
    if [ $file_count -eq 0 ]; then
        echo "  ⚠️  LM_DIR is EMPTY!"
    else
        echo "  📁 LM_DIR contents: $(ls -1 "${LM_DIR}" | head -3 | tr '\n' ' ')..."
    fi
else
    echo "✗ LM_DIR does NOT exist!"
fi

echo "Checking ITN_DIR: ${ITN_DIR}"
if [ -d "${ITN_DIR}" ]; then
    echo "✓ ITN_DIR exists"
    file_count=$(ls -1 "${ITN_DIR}" 2>/dev/null | wc -l)
    echo "  Files in ITN_DIR: $file_count"
    if [ $file_count -eq 0 ]; then
        echo "  ⚠️  ITN_DIR is EMPTY!"
    else
        echo "  📁 ITN_DIR contents: $(ls -1 "${ITN_DIR}" | head -3 | tr '\n' ' ')..."
    fi
else
    echo "✗ ITN_DIR does NOT exist!"
fi

echo "Checking HOTWORD_FILE: ${HOTWORD_FILE}"
if [ -f "${HOTWORD_FILE}" ]; then
    echo "✓ HOTWORD_FILE exists"
    file_size=$(stat -c%s "${HOTWORD_FILE}" 2>/dev/null || echo "unknown")
    echo "  File size: $file_size bytes"
else
    echo "✗ HOTWORD_FILE does NOT exist!"
fi

echo "============================================"

# Fix missing directories and files
echo "Fixing missing paths..."

# LM_DIR should already exist in the mapped models directory
if [ ! -d "${LM_DIR}" ]; then
    echo "⚠️  LM_DIR ${LM_DIR} does not exist!"
    echo "Available LM directories:"
    find /workspace/models -name "*lm*" -type d 2>/dev/null || echo "No LM directories found"
else
    echo "✓ LM_DIR already exists at ${LM_DIR}"
fi

# Hotword file should already exist in the container
if [ ! -f "${HOTWORD_FILE}" ]; then
    echo "⚠️  Hotword file not found, creating empty one..."
    touch "${HOTWORD_FILE}"
    echo "✓ Empty hotword file created at ${HOTWORD_FILE}"
else
    echo "✓ Hotword file already exists at ${HOTWORD_FILE}"
fi

echo "============================================"
echo "About to execute run_server_2pass.sh..."
exec bash run_server_2pass.sh \
    --certfile  "${CERTFILE}" \
    --port      "${PORT}" \
    --vad-dir   "${VAD_DIR}" \
    --model-dir "${MODEL_DIR}" \
    --online-model-dir "${ONLINE_MODEL_DIR}" \
    --punc-dir  "${PUNC_DIR}" \
    --lm-dir    "${LM_DIR}" \
    --itn-dir   "${ITN_DIR}" \
    --hotword   "${HOTWORD_FILE}" \


