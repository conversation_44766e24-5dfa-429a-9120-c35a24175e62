#!/bin/bash
set -e

echo "正在启动 FunASR WebSocket 服务器..."
echo "监听端口: ${PORT}"

# 切换到 FunASR 运行目录
cd /workspace/FunASR/runtime

echo "检查模型路径..."

echo "检查 VAD_DIR: ${VAD_DIR}"
if [ -d "${VAD_DIR}" ]; then
    echo "✓ VAD_DIR 存在"
else
    echo "✗ VAD_DIR 不存在!"
    exit 1
fi

echo "检查 MODEL_DIR: ${MODEL_DIR}"
if [ -d "${MODEL_DIR}" ]; then
    echo "✓ MODEL_DIR 存在"
else
    echo "✗ MODEL_DIR 不存在!"
    exit 1
fi

echo "检查 ONLINE_MODEL_DIR: ${ONLINE_MODEL_DIR}"
if [ -d "${ONLINE_MODEL_DIR}" ]; then
    echo "✓ ONLINE_MODEL_DIR 存在"
else
    echo "✗ ONLINE_MODEL_DIR 不存在!"
    exit 1
fi

echo "检查 PUNC_DIR: ${PUNC_DIR}"
if [ -d "${PUNC_DIR}" ]; then
    echo "✓ PUNC_DIR 存在"
else
    echo "✗ PUNC_DIR 不存在!"
    exit 1
fi

echo "检查 LM_DIR: ${LM_DIR}"
if [ -d "${LM_DIR}" ]; then
    echo "✓ LM_DIR 存在"
else
    echo "✗ LM_DIR 不存在!"
    exit 1
fi

echo "检查 ITN_DIR: ${ITN_DIR}"
if [ -d "${ITN_DIR}" ]; then
    echo "✓ ITN_DIR 存在"
else
    echo "✗ ITN_DIR 不存在!"
    exit 1
fi

echo "检查 HOTWORD_FILE: ${HOTWORD_FILE}"
if [ -f "${HOTWORD_FILE}" ]; then
    echo "✓ HOTWORD_FILE 存在"
else
    echo "警告: 热词文件不存在，创建空文件"
    touch "${HOTWORD_FILE}"
    echo "✓ 空热词文件已创建"
fi

echo "所有模型路径检查完成，启动服务器..."

# 启动 FunASR WebSocket 服务器并捕获输出
bash run_server_2pass.sh \
    --certfile  "${CERTFILE}" \
    --port      "${PORT}" \
    --vad-dir   "${VAD_DIR}" \
    --model-dir "${MODEL_DIR}" \
    --online-model-dir "${ONLINE_MODEL_DIR}" \
    --punc-dir  "${PUNC_DIR}" \
    --lm-dir    "${LM_DIR}" \
    --itn-dir   "${ITN_DIR}" \
    --hotword   "${HOTWORD_FILE}" 2>&1


tail -f /dev/null