#!/bin/bash
set -e

echo "=== FunASR Server Startup ==="
echo "CERTFILE: ${CERTFILE}"
echo "PORT: ${PORT}"
echo "VAD_DIR: ${VAD_DIR}"
echo "MODEL_DIR: ${MODEL_DIR}"
echo "ONLINE_MODEL_DIR: ${ONLINE_MODEL_DIR}"
echo "PUNC_DIR: ${PUNC_DIR}"
echo "LM_DIR: ${LM_DIR}"
echo "ITN_DIR: ${ITN_DIR}"
echo "HOTWORD_FILE: ${HOTWORD_FILE}"
echo "=========================="

cd /workspace/FunASR/runtime

echo "Current directory: $(pwd)"
echo "Starting server with custom parameters..."
echo "Command: bash run_server_2pass.sh --certfile '${CERTFILE}' --port '${PORT}' --vad_dir '${VAD_DIR}' --model_dir '${MODEL_DIR}' --online_model_dir '${ONLINE_MODEL_DIR}' --punc_dir '${PUNC_DIR}' --lm_dir '${LM_DIR}' --itn_dir '${ITN_DIR}' --hotword '${HOTWORD_FILE}'"

# Start the server with all custom parameters
exec bash run_server_2pass.sh \
    --certfile  "${CERTFILE}" \
    --port      "${PORT}" \
    --vad_dir   "${VAD_DIR}" \
    --model_dir "${MODEL_DIR}" \
    --online_model_dir "${ONLINE_MODEL_DIR}" \
    --punc_dir  "${PUNC_DIR}" \
    --lm_dir    "${LM_DIR}" \
    --itn_dir   "${ITN_DIR}" \
    --hotword   "${HOTWORD_FILE}"
