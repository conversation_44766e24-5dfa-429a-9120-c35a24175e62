#!/bin/bash
# FunASR WebSocket 服务器启动脚本
set -e

echo "正在启动 FunASR WebSocket 服务器..."
echo "监听端口: ${PORT}"

# 切换到 FunASR 运行目录
cd /workspace/FunASR/runtime

# 检查关键路径是否存在
echo "检查模型路径..."
for path in "${VAD_DIR}" "${MODEL_DIR}" "${PUNC_DIR}" "${LM_DIR}" "${ITN_DIR}"; do
    if [ ! -d "$path" ]; then
        echo "错误: 路径不存在 - $path"
        exit 1
    fi
done

if [ ! -f "${HOTWORD_FILE}" ]; then
    echo "警告: 热词文件不存在，创建空文件 - ${HOTWORD_FILE}"
    touch "${HOTWORD_FILE}"
fi

echo "所有模型路径检查完成，启动服务器..."

# 启动 FunASR WebSocket 服务器
bash run_server_2pass.sh \
    --certfile  "${CERTFILE}" \
    --port      "${PORT}" \
    --vad-dir   "${VAD_DIR}" \
    --model-dir "${MODEL_DIR}" \
    --online-model-dir "${ONLINE_MODEL_DIR}" \
    --punc-dir  "${PUNC_DIR}" \
    --lm-dir    "${LM_DIR}" \
    --itn-dir   "${ITN_DIR}" \
    --hotword   "${HOTWORD_FILE}"