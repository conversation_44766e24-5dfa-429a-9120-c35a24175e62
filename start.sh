#!/bin/bash
set -e

echo "=== FunASR Server Startup ==="
echo "CERTFILE: ${CERTFILE}"
echo "PORT: ${PORT}"
echo "VAD_DIR: ${VAD_DIR}"
echo "MODEL_DIR: ${MODEL_DIR}"
echo "ONLINE_MODEL_DIR: ${ONLINE_MODEL_DIR}"
echo "PUNC_DIR: ${PUNC_DIR}"
echo "LM_DIR: ${LM_DIR}"
echo "ITN_DIR: ${ITN_DIR}"
echo "HOTWORD_FILE: ${HOTWORD_FILE}"
echo "=========================="

cd /workspace/FunASR/runtime

echo "Current directory: $(pwd)"
echo "Checking if run_server_2pass.sh exists..."
if [ -f "run_server_2pass.sh" ]; then
    echo "✓ run_server_2pass.sh found"
    echo "File permissions: $(ls -la run_server_2pass.sh)"
else
    echo "✗ run_server_2pass.sh not found"
    echo "Directory contents:"
    ls -la
    exit 1
fi

# Build parameters array
PARAMS=()
PARAMS+=("--certfile" "${CERTFILE}")
PARAMS+=("--port" "${PORT}")

# Check if directories exist, if they do, add them to parameters
echo "Checking model directories..."

if [ -d "${VAD_DIR}" ]; then
    echo "✓ VAD_DIR ${VAD_DIR} exists"
    PARAMS+=("--vad_dir" "${VAD_DIR}")
else
    echo "⚠️  VAD_DIR ${VAD_DIR} does not exist, using default (will auto-download)"
fi

if [ -d "${MODEL_DIR}" ]; then
    echo "✓ MODEL_DIR ${MODEL_DIR} exists"
    PARAMS+=("--model_dir" "${MODEL_DIR}")
else
    echo "⚠️  MODEL_DIR ${MODEL_DIR} does not exist, using default (will auto-download)"
fi

if [ -d "${ONLINE_MODEL_DIR}" ]; then
    echo "✓ ONLINE_MODEL_DIR ${ONLINE_MODEL_DIR} exists"
    PARAMS+=("--online_model_dir" "${ONLINE_MODEL_DIR}")
else
    echo "⚠️  ONLINE_MODEL_DIR ${ONLINE_MODEL_DIR} does not exist, using default (will auto-download)"
fi

if [ -d "${PUNC_DIR}" ]; then
    echo "✓ PUNC_DIR ${PUNC_DIR} exists"
    PARAMS+=("--punc_dir" "${PUNC_DIR}")
else
    echo "⚠️  PUNC_DIR ${PUNC_DIR} does not exist, using default (will auto-download)"
fi

if [ -d "${LM_DIR}" ]; then
    echo "✓ LM_DIR ${LM_DIR} exists"
    PARAMS+=("--lm_dir" "${LM_DIR}")
else
    echo "⚠️  LM_DIR ${LM_DIR} does not exist, using default (will auto-download)"
fi

if [ -d "${ITN_DIR}" ]; then
    echo "✓ ITN_DIR ${ITN_DIR} exists"
    PARAMS+=("--itn_dir" "${ITN_DIR}")
else
    echo "⚠️  ITN_DIR ${ITN_DIR} does not exist, using default (will auto-download)"
fi

if [ -f "${HOTWORD_FILE}" ]; then
    echo "✓ HOTWORD_FILE ${HOTWORD_FILE} exists"
    PARAMS+=("--hotword" "${HOTWORD_FILE}")
else
    echo "⚠️  HOTWORD_FILE ${HOTWORD_FILE} does not exist, using default"
fi

echo "Starting server with parameters: ${PARAMS[@]}"
echo "Executing: bash run_server_2pass.sh ${PARAMS[@]}"

# Start the server
exec bash run_server_2pass.sh "${PARAMS[@]}"
