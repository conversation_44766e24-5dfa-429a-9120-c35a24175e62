(dp0
S'param_dict'
p1
(dp2
S'norm_type'
p3
S'clip_0to1'
p4
sS'score_clip'
p5
(lp6
F0.0
aF100.0
asS'C'
p7
F3.1
sS'nu'
p8
F0.9
sS'gamma'
p9
F0.1
ssS'model_dict'
p10
(dp11
S'feature_dict'
p12
(dp13
S'VMAF_feature'
p14
(lp15
S'vif'
p16
aS'adm'
p17
aS'motion'
p18
aS'ansnr'
p19
assg3
S'linear_rescale'
p20
sg5
g6
sS'feature_names'
p21
(lp22
S'VMAF_feature_adm_score'
p23
aS'VMAF_feature_ansnr_score'
p24
aS'VMAF_feature_motion_score'
p25
aS'VMAF_feature_vif_score'
p26
asS'intercepts'
p27
(lp28
F-0.14912280701754385
aF-1.635828565827225
aF-0.5027725296167747
aF-0.017141728475754268
aF-0.12191917348723096
asS'model_type'
p29
S'LIBSVMNUSVR'
p30
sS'model'
p31
NsS'slopes'
p32
(lp33
F0.010526315789473684
aF2.635828565827225
aF0.030306790717580585
aF0.05282785410063858
aF1.121919173487231
ass.