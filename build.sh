#!/bin/bash
# FunASR Docker 构建脚本

set -e

# 显示帮助信息
show_help() {
    echo "FunASR Docker 构建脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --with-models     构建包含 models 文件夹的版本（无需 models 目录映射）"
    echo "  --without-models  构建不包含 models 文件夹的版本（需要 models 目录映射，默认）"
    echo "  --tag TAG         指定镜像标签（默认: asr-base:latest 或 asr-all:latest）"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --without-models                    # 构建基础版本"
    echo "  $0 --with-models                       # 构建包含模型的版本 (asr-all:latest)"
    echo "  $0 --with-models --tag my-asr:v1.0     # 构建包含模型的版本并指定标签"
}

# 默认参数
INCLUDE_MODELS="false"
IMAGE_TAG="asr-base:latest"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --with-models)
            INCLUDE_MODELS="true"
            # 如果没有指定自定义标签，使用默认的 asr-all:latest
            if [ "$IMAGE_TAG" = "asr-base:latest" ]; then
                IMAGE_TAG="asr-all:latest"
            fi
            shift
            ;;
        --without-models)
            INCLUDE_MODELS="false"
            # 如果没有指定自定义标签，使用默认的 asr-base:latest
            if [ "$IMAGE_TAG" = "asr-all:latest" ]; then
                IMAGE_TAG="asr-base:latest"
            fi
            shift
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

echo "=========================================="
echo "FunASR Docker 构建配置"
echo "=========================================="
echo "包含 models 文件夹: $INCLUDE_MODELS"
echo "镜像标签: $IMAGE_TAG"
echo ""
echo "注意: 由于 Docker 限制，models 目录总是会被复制到镜像中"
echo "区别在于运行时是否需要目录映射来覆盖内置的 models"
echo "=========================================="

# 检查必需文件
echo "检查必需文件..."
required_files=("Dockerfile" "start.sh" "hotwords.txt" "ffmpeg" "ffprobe")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 缺少必需文件 $file"
        exit 1
    fi
done

# 如果包含 models，检查 models 目录
if [ "$INCLUDE_MODELS" = "true" ]; then
    echo "检查 models 目录..."
    if [ ! -d "models" ]; then
        echo "错误: 缺少 models 目录"
        exit 1
    fi

    # 检查关键模型文件
    echo "检查关键模型文件..."
    model_dirs=(
        "models/damo/speech_fsmn_vad_zh-cn-16k-common-onnx"
        "models/damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx"
        "models/thuduj12/fst_itn_zh"
        "models/lm/resource"
    )

    for dir in "${model_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            echo "警告: 模型目录 $dir 不存在"
        else
            file_count=$(ls -1 "$dir" 2>/dev/null | wc -l)
            echo "  ✓ $dir ($file_count 个文件)"
        fi
    done
fi

echo "开始构建 Docker 镜像..."

# 创建临时 Dockerfile
TEMP_DOCKERFILE="Dockerfile.tmp"

if [ "$INCLUDE_MODELS" = "true" ]; then
    echo "创建包含 models 的 Dockerfile..."
    # 创建包含 models 的版本
    cat > "$TEMP_DOCKERFILE" << 'EOF'
# 基于 FunASR 官方镜像
FROM registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-cpu-0.4.6

# 构建参数：是否包含模型文件
ARG INCLUDE_MODELS=false

# 安装 FFmpeg 静态编译版本
COPY ffmpeg /usr/local/bin/ffmpeg
COPY ffprobe /usr/local/bin/ffprobe
RUN chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe

# 环境变量配置
ENV CERTFILE=0 \
    VAD_DIR=/workspace/models/damo/speech_fsmn_vad_zh-cn-16k-common-onnx \
    MODEL_DIR=/workspace/save_model \
    ONLINE_MODEL_DIR=/workspace/save_model \
    PUNC_DIR=/workspace/models/damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx \
    LM_DIR=/workspace/models/lm/resource \
    ITN_DIR=/workspace/models/thuduj12/fst_itn_zh \
    HOTWORD_FILE=/workspace/hotwords.txt \
    PYTHONWARNINGS=ignore::RuntimeWarning \
    PYTHONDONTWRITEBYTECODE=1

# 暴露端口
EXPOSE 10098 10095

# 设置工作目录
WORKDIR /workspace/FunASR/runtime

# 复制启动脚本和配置文件
RUN chmod +x run_server_2pass.sh
COPY start.sh /start.sh
RUN chmod +x /start.sh
COPY hotwords.txt /workspace/hotwords.txt

# 复制 models 文件夹（完整版本）
COPY models/ /workspace/models/
RUN echo "models 文件夹已包含在镜像中" && \
    # 清理不必要的缓存和临时文件
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* && \
    # 清理 Python 缓存
    find /usr -name "*.pyc" -delete && \
    find /usr -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 启动服务
ENTRYPOINT ["/start.sh"]
EOF
else
    echo "创建基础版本的 Dockerfile..."
    # 使用原始 Dockerfile（不包含 models）
    cp Dockerfile "$TEMP_DOCKERFILE"
fi

# 构建 Docker 镜像
docker build \
    --build-arg INCLUDE_MODELS="$INCLUDE_MODELS" \
    -f "$TEMP_DOCKERFILE" \
    -t "$IMAGE_TAG" \
    .

# 清理临时文件
rm -f "$TEMP_DOCKERFILE"

echo "=========================================="
echo "构建完成！"
echo "=========================================="
echo "镜像标签: $IMAGE_TAG"

if [ "$INCLUDE_MODELS" = "true" ]; then
    echo "构建模式: 使用内置 models（无需 models 目录映射）"
    echo ""
    echo "运行命令:"
    echo "  docker run -d --name funasr-server \\"
    echo "    -v \"\$(pwd)/save_model:/workspace/save_model\" \\"
    echo "    -p 10098:10098 -p 10095:10095 \\"
    echo "    $IMAGE_TAG"
    echo ""
    echo "说明: 镜像已包含 models 文件夹，只需映射 save_model"
else
    echo "构建模式: 使用外部 models（需要 models 目录映射）"
    echo ""
    echo "运行命令:"
    echo "  docker run -d --name funasr-server \\"
    echo "    -v \"\$(pwd)/models:/workspace/models\" \\"
    echo "    -v \"\$(pwd)/save_model:/workspace/save_model\" \\"
    echo "    -p 10098:10098 -p 10095:10095 \\"
    echo "    $IMAGE_TAG"
    echo ""
    echo "说明: 外部 models 目录映射会覆盖镜像内置的 models"
fi

echo ""
echo "或使用 Docker Compose:"
echo "  docker-compose up -d"
echo "=========================================="
