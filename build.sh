#!/bin/bash
# FunASR Docker 构建脚本

set -e

# 显示帮助信息
show_help() {
    echo "FunASR Docker 构建脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --with-models     构建包含 models 文件夹的版本（无需 models 目录映射）"
    echo "  --without-models  构建不包含 models 文件夹的版本（需要 models 目录映射，默认）"
    echo "  --tag TAG         指定镜像标签（默认: asr-cpu:latest）"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --without-models                    # 构建基础版本"
    echo "  $0 --with-models                       # 构建包含模型的版本"
    echo "  $0 --with-models --tag asr-cpu:full    # 构建包含模型的版本并指定标签"
}

# 默认参数
INCLUDE_MODELS="false"
IMAGE_TAG="asr-cpu:latest"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --with-models)
            INCLUDE_MODELS="true"
            shift
            ;;
        --without-models)
            INCLUDE_MODELS="false"
            shift
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

echo "=========================================="
echo "FunASR Docker 构建配置"
echo "=========================================="
echo "包含 models 文件夹: $INCLUDE_MODELS"
echo "镜像标签: $IMAGE_TAG"
echo ""
echo "注意: 由于 Docker 限制，models 目录总是会被复制到镜像中"
echo "区别在于运行时是否需要目录映射来覆盖内置的 models"
echo "=========================================="

# 检查必需文件
echo "检查必需文件..."
required_files=("Dockerfile" "start.sh" "hotwords.txt" "ffmpeg" "ffprobe")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 缺少必需文件 $file"
        exit 1
    fi
done

# 如果包含 models，检查 models 目录
if [ "$INCLUDE_MODELS" = "true" ]; then
    echo "检查 models 目录..."
    if [ ! -d "models" ]; then
        echo "错误: 缺少 models 目录"
        exit 1
    fi

    # 检查关键模型文件
    echo "检查关键模型文件..."
    model_dirs=(
        "models/damo/speech_fsmn_vad_zh-cn-16k-common-onnx"
        "models/damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx"
        "models/thuduj12/fst_itn_zh"
        "models/lm/resource"
    )

    for dir in "${model_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            echo "警告: 模型目录 $dir 不存在"
        else
            file_count=$(ls -1 "$dir" 2>/dev/null | wc -l)
            echo "  ✓ $dir ($file_count 个文件)"
        fi
    done
fi

echo "开始构建 Docker 镜像..."

# 构建 Docker 镜像
docker build \
    --build-arg INCLUDE_MODELS="$INCLUDE_MODELS" \
    -t "$IMAGE_TAG" \
    .

echo "=========================================="
echo "构建完成！"
echo "=========================================="
echo "镜像标签: $IMAGE_TAG"

if [ "$INCLUDE_MODELS" = "true" ]; then
    echo "构建模式: 使用内置 models（无需 models 目录映射）"
    echo ""
    echo "运行命令:"
    echo "  docker run -d --name funasr-server \\"
    echo "    -v \"\$(pwd)/save_model:/workspace/save_model\" \\"
    echo "    -p 10098:10098 -p 10095:10095 \\"
    echo "    $IMAGE_TAG"
    echo ""
    echo "说明: 镜像已包含 models 文件夹，只需映射 save_model"
else
    echo "构建模式: 使用外部 models（需要 models 目录映射）"
    echo ""
    echo "运行命令:"
    echo "  docker run -d --name funasr-server \\"
    echo "    -v \"\$(pwd)/models:/workspace/models\" \\"
    echo "    -v \"\$(pwd)/save_model:/workspace/save_model\" \\"
    echo "    -p 10098:10098 -p 10095:10095 \\"
    echo "    $IMAGE_TAG"
    echo ""
    echo "说明: 外部 models 目录映射会覆盖镜像内置的 models"
fi

echo ""
echo "或使用 Docker Compose:"
echo "  docker-compose up -d"
echo "=========================================="
