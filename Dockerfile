FROM registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-cpu-0.4.6

ENV CERTFILE=0 \
    PORT=10098 \
    VAD_DIR=/workspace/models/damo/speech_fsmn_vad_zh-cn-16k-common-onnx \
    MODEL_DIR=/workspace/save_model \
    ONLINE_MODEL_DIR=/workspace/save_model \
    PUNC_DIR=/workspace/models/damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx \
    LM_DIR=/workspace/models/lm/resource \
    ITN_DIR=/workspace/models/thuduj12/fst_itn_zh \
    HOTWORD_FILE=/workspace/hotwords.txt

EXPOSE ${PORT}

WORKDIR /workspace/FunASR/runtime

# Ensure scripts have execute permissions
RUN chmod +x run_server_2pass.sh

COPY start.sh /start.sh
RUN chmod +x /start.sh

# Copy hotwords.txt from current directory to the container
COPY hotwords.txt /workspace/hotwords.txt

ENTRYPOINT ["/start.sh"]
