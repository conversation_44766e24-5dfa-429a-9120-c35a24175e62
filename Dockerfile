# 基于 FunASR 官方镜像
FROM registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-cpu-0.4.6

# 构建参数：是否包含模型文件
ARG INCLUDE_MODELS=false

# 安装 FFmpeg 静态编译版本
COPY ffmpeg /usr/local/bin/ffmpeg
COPY ffprobe /usr/local/bin/ffprobe
RUN chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe

# 环境变量配置
ENV CERTFILE=0 \
    VAD_DIR=/workspace/models/damo/speech_fsmn_vad_zh-cn-16k-common-onnx \
    MODEL_DIR=/workspace/save_model \
    ONLINE_MODEL_DIR=/workspace/save_model \
    PUNC_DIR=/workspace/models/damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx \
    LM_DIR=/workspace/models/lm/resource \
    ITN_DIR=/workspace/models/thuduj12/fst_itn_zh \
    HOTWORD_FILE=/workspace/hotwords.txt \
    PYTHONWARNINGS=ignore::RuntimeWarning \
    PYTHONDONTWRITEBYTECODE=1

# 暴露端口
EXPOSE 10098 10095

# 设置工作目录
WORKDIR /workspace/FunASR/runtime

# 复制启动脚本和配置文件
RUN chmod +x run_server_2pass.sh
COPY start.sh /start.sh
RUN chmod +x /start.sh
COPY hotwords.txt /workspace/hotwords.txt

# 条件复制 models 文件夹（仅在 INCLUDE_MODELS=true 时）
RUN if [ "$INCLUDE_MODELS" = "true" ]; then \
        echo "需要包含 models 文件夹，但 Docker 构建时无法条件复制"; \
        echo "请使用 Dockerfile.with-models 来构建包含 models 的版本"; \
        exit 1; \
    else \
        echo "跳过 models 文件夹复制，使用目录映射"; \
    fi

# 启动服务
ENTRYPOINT ["/start.sh"]
