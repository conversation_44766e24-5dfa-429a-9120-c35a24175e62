# 基于 FunASR 官方镜像
FROM registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-cpu-0.4.6

# 构建参数：是否包含模型文件
ARG INCLUDE_MODELS=false

# 安装 FFmpeg 静态编译版本
COPY ffmpeg /usr/local/bin/ffmpeg
COPY ffprobe /usr/local/bin/ffprobe
RUN chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe

# 环境变量配置
ENV CERTFILE=0 \
    VAD_DIR=/workspace/models/damo/speech_fsmn_vad_zh-cn-16k-common-onnx \
    MODEL_DIR=/workspace/save_model \
    ONLINE_MODEL_DIR=/workspace/save_model \
    PUNC_DIR=/workspace/models/damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx \
    LM_DIR=/workspace/models/lm/resource \
    ITN_DIR=/workspace/models/thuduj12/fst_itn_zh \
    HOTWORD_FILE=/workspace/hotwords.txt \
    PYTHONWARNINGS=ignore::RuntimeWarning \
    PYTHONDONTWRITEBYTECODE=1

# 暴露端口
EXPOSE 10098 10095

# 设置工作目录
WORKDIR /workspace/FunASR/runtime

# 复制启动脚本和配置文件
RUN chmod +x run_server_2pass.sh
COPY start.sh /start.sh
RUN chmod +x /start.sh
COPY hotwords.txt /workspace/hotwords.txt

# 条件复制模型文件（如果 INCLUDE_MODELS=true）
COPY models/ /tmp/models_temp/
COPY save_model/ /tmp/save_model_temp/
RUN if [ "$INCLUDE_MODELS" = "true" ]; then \
        echo "复制模型文件到容器内..." && \
        mkdir -p /workspace/models /workspace/save_model && \
        cp -r /tmp/models_temp/* /workspace/models/ && \
        cp -r /tmp/save_model_temp/* /workspace/save_model/ && \
        echo "模型文件复制完成"; \
    else \
        echo "跳过模型文件复制，使用目录映射"; \
    fi && \
    rm -rf /tmp/models_temp /tmp/save_model_temp

# 启动服务
ENTRYPOINT ["/start.sh"]
