# FunASR Docker 语音识别服务

基于 FunASR 的 Docker 容器，集成 FFmpeg 支持，提供实时语音识别服务。

## 功能特性

- **FunASR WebSocket 服务器**: 支持双路解码的实时语音识别
- **FFmpeg 集成**: 静态编译的 FFmpeg 7.0.2，支持音频处理
- **预配置模型**: 支持 VAD、ASR、标点、ITN 和语言模型
- **热词支持**: 内置医学术语热词文件
- **WebSocket API**: 实时流式语音识别接口

## 快速开始

### 系统要求

- 已安装 Docker 和 Docker Compose
- 正确的模型文件目录结构
- `models/` 目录中的音频模型文件
- `save_model/` 目录中的训练ASR模型

### 目录结构

```
docker/
├── Dockerfile              # Docker 构建文件
├── docker-compose.yml      # Docker Compose 配置
├── start.sh                # 启动脚本
├── hotwords.txt            # 热词文件
├── ffmpeg                  # FFmpeg 二进制文件
├── ffprobe                 # FFprobe 二进制文件
├── models/                 # 模型文件目录
│   ├── damo/
│   │   ├── speech_fsmn_vad_zh-cn-16k-common-onnx/
│   │   └── punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx/
│   ├── thuduj12/
│   │   └── fst_itn_zh/
│   └── lm/
│       └── resource/
└── save_model/             # 训练模型目录
    └── [你的训练ASR模型]
```

### 构建和运行

#### 方法一: 使用构建脚本 (推荐)

**基础版本 (需要目录映射):**
```bash
./build.sh --without-models
```

**包含 models 的版本 (无需 models 目录映射):**
```bash
./build.sh --with-models
```

**自定义标签:**
```bash
./build.sh --with-models --tag asr-cpu:full
```

#### 方法二: 使用 Docker Compose

1. **启动服务:**
   ```bash
   docker-compose up -d
   ```

2. **查看日志:**
   ```bash
   docker-compose logs -f
   ```

3. **停止服务:**
   ```bash
   docker-compose down
   ```

#### 方法三: 手动 Docker 命令

**基础版本构建:**
```bash
docker build -t asr-cpu:latest .
```

**包含 models 版本构建:**
```bash
docker build --build-arg INCLUDE_MODELS=true -t asr-cpu:full .
```

**运行基础版本 (需要目录映射):**
```bash
docker run -d \
  --name funasr-server \
  -v "$(pwd)/models:/workspace/models" \
  -v "$(pwd)/save_model:/workspace/save_model" \
  -p 10098:10098 \
  -p 10095:10095 \
  asr-cpu:latest
```

**运行包含 models 的版本 (只需 save_model 映射):**
```bash
docker run -d \
  --name funasr-server \
  -v "$(pwd)/save_model:/workspace/save_model" \
  -p 10098:10098 \
  -p 10095:10095 \
  asr-cpu:full
```

**查看状态:**
```bash
docker ps
docker logs funasr-server
```

### 配置说明

容器使用以下环境变量进行配置:

- `CERTFILE`: SSL证书文件 (默认: 0 表示不使用SSL)
- `PORT`: WebSocket服务器端口 (默认: 10098)
- `VAD_DIR`: 语音活动检测模型目录
- `MODEL_DIR`: 主ASR模型目录
- `ONLINE_MODEL_DIR`: 在线ASR模型目录
- `PUNC_DIR`: 标点符号模型目录
- `LM_DIR`: 语言模型目录
- `ITN_DIR`: 逆文本标准化模型目录
- `HOTWORD_FILE`: 热词文件路径

### 使用方法

容器启动后，可以通过以下地址连接WebSocket服务器:

**主要服务端口:**
```
ws://localhost:10098  # FunASR WebSocket 主服务
```

**额外服务端口:**
```
ws://localhost:10095  # 额外服务端口
```

服务器支持实时音频流语音识别。

### FFmpeg 使用

容器内可以使用 FFmpeg 进行音频处理:

```bash
# 查看 FFmpeg 版本
docker exec funasr-server ffmpeg -version

# 处理音频文件
docker exec funasr-server ffmpeg -i input.wav -ar 16000 -ac 1 output.wav
```

## 故障排除

### 容器立即退出
- 检查所有模型目录是否存在并包含必需文件
- 验证目录映射是否正确
- 查看容器日志: `docker logs funasr-server`

### 模型加载失败
- 确保模型文件在正确的目录结构中
- 检查映射目录的文件权限
- 验证模型文件完整性

### WebSocket 连接问题
- 确认容器正在运行: `docker ps`
- 检查端口映射: `-p 10098:10098 -p 10095:10095`
- 验证防火墙设置

## 模型要求

容器需要以下模型文件:

1. **VAD模型**: 语音活动检测
2. **ASR模型**: 离线和在线语音识别模型
3. **标点模型**: 用于为转录文本添加标点符号
4. **语言模型**: 用于提高识别准确性
5. **ITN模型**: 逆文本标准化

## 许可证

本项目使用 FunASR，遵循 Apache License 2.0 许可证。
FFmpeg 根据配置遵循 LGPL/GPL 许可证。
