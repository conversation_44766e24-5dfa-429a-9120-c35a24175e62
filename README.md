# FunASR Docker with FFmpeg

A Docker container for FunASR (Fun Automatic Speech Recognition) with FFmpeg support for audio processing.

## Features

- **FunASR WebSocket Server**: Real-time speech recognition with 2-pass decoding
- **FFmpeg Integration**: Static compiled FFmpeg 7.0.2 for audio processing
- **Pre-configured Models**: VAD, ASR, Punctuation, ITN, and Language Model support
- **Hotword Support**: Custom hotword file for medical terminology
- **WebSocket API**: Real-time streaming speech recognition

## Quick Start

### Prerequisites

- Docker installed on your system
- Model files in the correct directory structure
- Audio model files in `models/` directory
- Trained ASR models in `save_model/` directory

### Directory Structure

```
docker/
├── Dockerfile
├── start.sh
├── hotwords.txt
├── ffmpeg
├── ffprobe
├── models/
│   ├── damo/
│   │   ├── speech_fsmn_vad_zh-cn-16k-common-onnx/
│   │   └── punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx/
│   ├── thuduj12/
│   │   └── fst_itn_zh/
│   └── lm/
│       └── resource/
└── save_model/
    └── [your trained ASR models]
```

### Build and Run

1. **Build the Docker image:**
   ```bash
   docker build -t asr-cpu:latest .
   ```

2. **Run the container:**
   ```bash
   docker run -d \
     --name asr-server \
     -v "$(pwd)/models:/workspace/models" \
     -v "$(pwd)/save_model:/workspace/save_model" \
     -p 10098:10098 \
     asr-cpu:latest
   ```

3. **Check container status:**
   ```bash
   docker ps
   docker logs asr-server
   ```

### Configuration

The container uses the following environment variables:

- `CERTFILE`: SSL certificate file (default: 0 for no SSL)
- `PORT`: WebSocket server port (default: 10098)
- `VAD_DIR`: Voice Activity Detection model directory
- `MODEL_DIR`: Main ASR model directory
- `ONLINE_MODEL_DIR`: Online ASR model directory
- `PUNC_DIR`: Punctuation model directory
- `LM_DIR`: Language model directory
- `ITN_DIR`: Inverse Text Normalization model directory
- `HOTWORD_FILE`: Hotword file path

### Usage

Once the container is running, you can connect to the WebSocket server at:
```
ws://localhost:10098
```

The server supports real-time audio streaming for speech recognition.

### FFmpeg Usage

FFmpeg is available inside the container for audio processing:

```bash
# Check FFmpeg version
docker exec asr-server ffmpeg -version

# Process audio files
docker exec asr-server ffmpeg -i input.wav -ar 16000 -ac 1 output.wav
```

## Troubleshooting

### Container Exits Immediately
- Check that all model directories exist and contain the required files
- Verify the volume mounts are correct
- Check container logs: `docker logs asr-server`

### Models Not Loading
- Ensure model files are in the correct directory structure
- Check file permissions on mounted volumes
- Verify model file integrity

### WebSocket Connection Issues
- Confirm the container is running: `docker ps`
- Check port mapping: `-p 10098:10098`
- Verify firewall settings

## Model Requirements

The container expects the following model files:

1. **VAD Model**: Voice Activity Detection
2. **ASR Models**: Offline and online speech recognition models
3. **Punctuation Model**: For adding punctuation to transcripts
4. **Language Model**: For improved recognition accuracy
5. **ITN Model**: Inverse Text Normalization

## License

This project uses FunASR which is licensed under the Apache License 2.0.
FFmpeg is licensed under LGPL/GPL depending on configuration.
