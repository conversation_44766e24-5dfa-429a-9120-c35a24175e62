#!/bin/bash

# Custom FunASR server startup script with better parameter handling

# Default values
download_model_dir="/workspace/models"
model_dir="damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-onnx"
online_model_dir="damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-online-onnx"
vad_dir="damo/speech_fsmn_vad_zh-cn-16k-common-onnx"
punc_dir="damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx"
itn_dir="thuduj12/fst_itn_zh"
lm_dir="damo/speech_ngram_lm_zh-cn-ai-wesp-fst"
port=10095
certfile="$(pwd)/ssl_key/server.crt"
keyfile="$(pwd)/ssl_key/server.key"
hotword="$(pwd)/websocket/hotwords.txt"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --download-model-dir|--download_model_dir)
            download_model_dir="$2"
            shift 2
            ;;
        --model-dir|--model_dir)
            model_dir="$2"
            shift 2
            ;;
        --online-model-dir|--online_model_dir)
            online_model_dir="$2"
            shift 2
            ;;
        --vad-dir|--vad_dir)
            vad_dir="$2"
            shift 2
            ;;
        --punc-dir|--punc_dir)
            punc_dir="$2"
            shift 2
            ;;
        --itn-dir|--itn_dir)
            itn_dir="$2"
            shift 2
            ;;
        --lm-dir|--lm_dir)
            lm_dir="$2"
            shift 2
            ;;
        --port)
            port="$2"
            shift 2
            ;;
        --certfile)
            certfile="$2"
            shift 2
            ;;
        --keyfile)
            keyfile="$2"
            shift 2
            ;;
        --hotword)
            hotword="$2"
            shift 2
            ;;
        *)
            echo "Unknown parameter: $1"
            shift
            ;;
    esac
done

echo "=== FunASR Custom Server Configuration ==="
echo "download_model_dir: $download_model_dir"
echo "model_dir: $model_dir"
echo "online_model_dir: $online_model_dir"
echo "vad_dir: $vad_dir"
echo "punc_dir: $punc_dir"
echo "itn_dir: $itn_dir"
echo "lm_dir: $lm_dir"
echo "port: $port"
echo "certfile: $certfile"
echo "keyfile: $keyfile"
echo "hotword: $hotword"
echo "=========================================="

# Set decoder_thread_num
decoder_thread_num=$(cat /proc/cpuinfo | grep "processor"|wc -l) || { echo "Get cpuinfo failed. Set decoder_thread_num = 32"; decoder_thread_num=32; }
multiple_io=16
io_thread_num=$(( (decoder_thread_num + multiple_io - 1) / multiple_io ))
model_thread_num=1
cmd_path=/workspace/FunASR/runtime/websocket/build/bin
cmd=funasr-wss-server-2pass

echo "decoder_thread_num: $decoder_thread_num"
echo "io_thread_num: $io_thread_num"
echo "model_thread_num: $model_thread_num"

# Handle SSL certificate
if [ -z "$certfile" ] || [ "$certfile" = "0" ]; then
  echo "SSL disabled (certfile is empty or '0')"
  certfile=""
  keyfile=""
else
  echo "SSL enabled with certfile: $certfile"
fi

# Change to command directory
echo "Changing to directory: $cmd_path"
cd $cmd_path

# Build the command
echo "Executing command: $cmd_path/${cmd}"
echo "Parameters:"
echo "  --download-model-dir: $download_model_dir"
echo "  --model-dir: $model_dir"
echo "  --online-model-dir: $online_model_dir"
echo "  --vad-dir: $vad_dir"
echo "  --punc-dir: $punc_dir"
echo "  --itn-dir: $itn_dir"
echo "  --lm-dir: $lm_dir"
echo "  --decoder-thread-num: $decoder_thread_num"
echo "  --model-thread-num: $model_thread_num"
echo "  --io-thread-num: $io_thread_num"
echo "  --port: $port"
echo "  --certfile: $certfile"
echo "  --keyfile: $keyfile"
echo "  --hotword: $hotword"

# Execute the server
exec $cmd_path/${cmd} \
  --download-model-dir "${download_model_dir}" \
  --model-dir "${model_dir}" \
  --online-model-dir "${online_model_dir}" \
  --vad-dir "${vad_dir}" \
  --punc-dir "${punc_dir}" \
  --itn-dir "${itn_dir}" \
  --lm-dir "${lm_dir}" \
  --decoder-thread-num ${decoder_thread_num} \
  --model-thread-num ${model_thread_num} \
  --io-thread-num  ${io_thread_num} \
  --port ${port} \
  --certfile  "${certfile}" \
  --keyfile "${keyfile}" \
  --hotword "${hotword}"
